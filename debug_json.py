#!/usr/bin/env python3

import os
import sys
import openai
from ai_utility_orchestrator.utils.response_formatter import format_response

# Test the JSON response format directly
def test_json_format():
    print("Testing JSON format response...")
    
    system_prompt = """You are a strict JSON-only tool selector. You MUST respond with EXACTLY this JSON format:
    {"selected_tool": "ai_universal_processor", "parameters": {"request": "user_request_here", "context": "business request", "data": {}}, "reasoning": "explanation"}
    
    CRITICAL RULES:
    - Use "selected_tool" NOT "tool"
    - Use "parameters" NOT "input" 
    - Always select "ai_universal_processor"
    - NO other text, explanations, or formatting
    - ONLY valid JSON response"""
    
    prompt = """Available tools:
• ai_universal_processor: Universal AI-powered tool that dynamically handles any request

User request: "Calculate ROI for $10000 investment returning $12500"

CRITICAL: You MUST respond with EXACTLY this JSON structure. Do not add any other text before or after:

{"selected_tool": "ai_universal_processor", "parameters": {"request": "Calculate ROI for $10000 investment returning $12500", "context": "business request", "data": {}}, "reasoning": "Processing request with universal AI tool"}

IMPORTANT: Use "selected_tool" not "tool". Use "parameters" not "input"."""
    
    try:
        result = format_response(
            prompt=prompt,
            formatter="json",
            model_name="gpt-4o-mini",
            temperature=0.0,
            return_meta=True,
            system_prompt=system_prompt
        )
        
        print("Raw response:", result.get("raw_response", "No raw response"))
        print("Parsed response:", result.get("parsed_response", "No parsed response"))
        print("Error:", result.get("error", "No error"))
        
        # Test the extraction function
        from ai_utility_orchestrator.core.agent_builder import _extract_tool_decision
        
        raw_response = result.get("raw_response", "")
        decision = _extract_tool_decision(raw_response)
        print("Extracted decision:", decision)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_json_format()
