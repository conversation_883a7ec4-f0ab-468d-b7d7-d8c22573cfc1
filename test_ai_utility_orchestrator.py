#!/usr/bin/env python3
"""
AI Utility Orchestrator - Comprehensive Test Suite
==================================================

Tests all core functionality of the AI Utility Orchestrator package using REAL OpenAI API:
- Package imports and basic functionality
- Tool creation and registration
- Agent execution with real LLM responses
- Context management
- Error handling
- Multi-tool scenarios

Author: <PERSON><PERSON><PERSON>
"""

import pytest
import json
import os
import time
from ai_utility_orchestrator import (
    agent_executor,
    ToolRegistry,
    ContextManager,
    create_tool_from_function,
    Tool
)

# Ensure OpenAI API key is available for testing
def setup_module():
    """Setup for the entire test module."""
    if not os.getenv("OPENAI_API_KEY"):
        pytest.skip("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
    print("🔑 Using real OpenAI API for testing - this will make actual API calls")


class TestPackageImports:
    """Test that all required components can be imported successfully."""
    
    def test_core_imports(self):
        """Test core component imports."""
        from ai_utility_orchestrator import agent_executor, ToolRegistry
        assert callable(agent_executor)
        assert ToolRegistry is not None
    
    def test_tool_imports(self):
        """Test tool-related imports."""
        from ai_utility_orchestrator import create_tool_from_function, Tool
        assert callable(create_tool_from_function)
        assert Tool is not None
    
    def test_utility_imports(self):
        """Test utility imports."""
        from ai_utility_orchestrator import ContextManager
        assert ContextManager is not None


class TestToolCreation:
    """Test tool creation and registration functionality."""
    
    def test_simple_tool_creation(self):
        """Test creating a simple tool from a function."""
        def sample_tool(params):
            return f"Processed: {params.get('input', 'none')}"
        
        tool = create_tool_from_function(
            sample_tool,
            name="sample_tool",
            description="A sample tool for testing"
        )
        
        assert tool.name == "sample_tool"
        assert tool.description == "A sample tool for testing"
        assert callable(tool.execute)
    
    def test_tool_with_schema(self):
        """Test creating a tool with custom schema."""
        def calculator(params):
            expression = params.get("expression", "0")
            return f"Result: {eval(expression)}"
        
        schema = {
            "type": "object",
            "properties": {
                "expression": {"type": "string"}
            },
            "required": ["expression"]
        }
        
        tool = create_tool_from_function(
            calculator,
            name="calculator",
            description="Performs calculations",
            schema=schema
        )
        
        assert tool.schema == schema
        result = tool.execute({"expression": "2 + 2"})
        assert "4" in result
    
    def test_tool_registry(self):
        """Test tool registry functionality."""
        registry = ToolRegistry()
        
        def test_func(params):
            return "test result"
        
        tool = create_tool_from_function(test_func, name="test_tool", description="Test tool")
        registry.register_tool(tool)
        
        assert "test_tool" in registry.list_tools()
        retrieved_tool = registry.get_tool("test_tool")
        assert retrieved_tool.name == "test_tool"


class TestContextManager:
    """Test context management functionality."""
    
    def test_context_creation(self):
        """Test creating and using context manager."""
        cm = ContextManager()
        
        # Set context
        cm.set_context("user_id", "test_user")
        cm.set_context("preferences", {"theme": "dark"})
        
        # Get context
        assert cm.get_context("user_id") == "test_user"
        assert cm.get_context("preferences")["theme"] == "dark"
        assert cm.get_context("nonexistent") is None
    
    def test_context_operations(self):
        """Test various context operations."""
        cm = ContextManager()
        
        # Set multiple contexts
        cm.set_context("key1", "value1")
        cm.set_context("key2", {"nested": "data"})
        
        # Get all context
        all_context = cm.get_all_context()
        assert "key1" in all_context
        assert "key2" in all_context
        
        # Remove context
        cm.remove_context("key1")
        assert cm.get_context("key1") is None
        assert cm.get_context("key2") is not None


class TestAgentExecution:
    """Test agent execution using real OpenAI API - matches README examples."""
    
    def create_test_config(self):
        """Create a test configuration matching README examples."""
        return {
            "llm": {
                "model": "gpt-4o-mini",  # User chooses model (as per README)
                "temperature": 0.7       # User sets temperature (as per README)
            },
            "system_prompt": "You are my personal AI assistant.",  # README example
            "context_limit": 5,
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": False
            },
            # All prompt templates must be provided (as per README)
            "no_tools_prompt": "Analyze this request: {user_input}. I don't have specific tools available, but I can provide general assistance.",
            "tool_selection_prompt": """Available tools: {tools_text}
User request: "{user_input}"

Please select the most appropriate tool and provide parameters in JSON format:
{{"selected_tool": "tool_name", "parameters": {{}}, "reasoning": "explanation"}}""",
            "tool_description_template": "• {name}: {description}",
            # Optional error handling templates (as per README)
            "error_response_template": "Error with {tool_name}: {error}",
            "llm_error_message": "Processing error occurred",
            "tools": []
        }
    
    def test_agent_execution_no_tools(self):
        """Test agent execution when no tools are available."""
        config = self.create_test_config()

        result = agent_executor(
            user_input="Hello, how are you?",
            config=config,
            user_id="test_user"
        )

        assert result["input"] == "Hello, how are you?"
        assert result["selected_tool"] == "none"
        assert result["user_id"] == "test_user"
        assert "final_response" in result
        assert len(result["final_response"]) > 0
    
    def test_agent_execution_with_tools(self):
        """Test agent execution with available tools - matches README example."""
        config = self.create_test_config()

        # Create calculator tool matching README example
        def my_calculator(params):
            """Calculator function matching README example."""
            expression = params.get("expression", "0")
            try:
                result = eval(expression)  # README uses eval example
                return f"Result: {result}"  # README format: "Result: {eval(expression)}"
            except Exception as e:
                return f"Calculation error: {str(e)}"

        # Create tool using README approach
        tool = create_tool_from_function(
            my_calculator,
            name="calculator",
            description="Performs calculations",  # README description
            schema={  # README includes schema
                "type": "object",
                "properties": {
                    "expression": {"type": "string"}
                },
                "required": ["expression"]
            }
        )
        config["tools"] = [tool]

        # Test with README example: "Calculate 15 * 8"
        print("\n🧮 Testing calculator tool (README example)...")
        result = agent_executor(
            user_input="Calculate 15 * 8",  # README example
            config=config,
            user_id="test_user"
        )

        print(f"Tool selected: {result['selected_tool']}")
        print(f"Parameters: {result['tool_parameters']}")
        print(f"Response: {result['final_response']}")

        # Verify the result structure
        assert result["input"] == "Calculate 15 * 8"
        assert result["user_id"] == "test_user"
        assert "final_response" in result
        assert len(result["final_response"]) > 0

        # Add delay for API rate limiting
        time.sleep(1)
    
    def test_agent_execution_missing_config(self):
        """Test agent execution with missing configuration."""
        with pytest.raises(ValueError, match="Configuration must be provided"):
            agent_executor("test input", config=None)
    
    def test_agent_execution_invalid_config(self):
        """Test agent execution with invalid configuration."""
        invalid_config = {"invalid": "config"}
        
        with pytest.raises(ValueError, match="LLM configuration must be provided"):
            agent_executor("test input", config=invalid_config)


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_tool_execution_error(self):
        """Test handling of tool execution errors."""
        def failing_tool(params):
            raise Exception("Tool execution failed")
        
        tool = create_tool_from_function(
            failing_tool,
            name="failing_tool",
            description="A tool that fails"
        )
        
        # Test that tool creation succeeds but execution fails gracefully
        assert tool.name == "failing_tool"
        
        with pytest.raises(Exception):
            tool.execute({})
    
    def test_invalid_tool_creation(self):
        """Test invalid tool creation scenarios."""
        def test_func(params):
            return "test"

        # Test missing name - should use function name if not provided
        tool = create_tool_from_function(test_func, name=None, description="test")
        assert tool.name == "test_func"  # Uses function name

        # Test missing description - should raise error
        with pytest.raises(ValueError):
            create_tool_from_function(test_func, name="test", description=None)


class TestIntegrationScenarios:
    """Test realistic integration scenarios using real OpenAI API."""

    def test_multi_tool_scenario(self):
        """Test scenario with multiple tools available."""
        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.3},
            "system_prompt": "You are a helpful assistant that selects the most appropriate tool for user requests.",
            "context_limit": 5,
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "no_tools_prompt": "I understand your request: {user_input}. However, I don't have specific tools available to help with this.",
            "tool_selection_prompt": """
Available tools:
{tools_text}

User request: "{user_input}"

Please select the most appropriate tool and provide parameters in JSON format:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "explanation"}}
            """,
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }

        # Create multiple tools
        def calculator(params):
            expression = params.get('expression', '0')
            try:
                result = eval(expression)
                return f"Math calculation result: {expression} = {result}"
            except:
                return f"Could not calculate: {expression}"

        def weather_info(params):
            city = params.get('city', 'Unknown')
            # Simulate weather data
            return f"Weather information for {city}: Temperature 72°F, Sunny skies, Light breeze"

        tools = [
            create_tool_from_function(calculator, name="calculator", description="Performs mathematical calculations and arithmetic operations"),
            create_tool_from_function(weather_info, name="weather", description="Provides weather information for specified cities")
        ]
        config["tools"] = tools

        # Test with a clear weather request
        print("\n🌤️ Testing weather tool selection...")
        result = agent_executor(
            user_input="What's the weather like in San Francisco?",
            config=config
        )

        print(f"Tool selected: {result['selected_tool']}")
        print(f"Parameters: {result['tool_parameters']}")
        print(f"Response: {result['final_response']}")

        # Verify basic response structure
        assert "final_response" in result
        assert len(result["final_response"]) > 0

        # Add small delay to respect API rate limits
        time.sleep(1)

    def test_context_aware_execution(self):
        """Test execution with context manager using real OpenAI API."""
        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.3},
            "system_prompt": "You are a helpful assistant that can use context information.",
            "context_limit": 5,
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "no_tools_prompt": "I understand your request: {user_input}. However, I don't have specific tools available to help with this.",
            "tool_selection_prompt": """
Available tools:
{tools_text}

User request: "{user_input}"

Please select the most appropriate tool and provide parameters in JSON format:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "explanation"}}
            """,
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }

        def context_tool(params):
            """Context-aware tool matching README example."""
            # Access context manager from params (as per README)
            context_manager = params.get("context_manager")
            if context_manager:
                # Get data from context (README example)
                user_id = context_manager.get_context("user_id")
                user_prefs = context_manager.get_context("preferences", {})
                theme = user_prefs.get('theme', 'default')
                # Store data in context (README example)
                context_manager.set_context("last_tool", "context_tool")
                return f"Hello user {user_id}! I see you prefer {theme} theme. Tool executed successfully."
            return "No context information available"

        tool = create_tool_from_function(
            context_tool,
            name="context_tool",
            description="Accesses user context and preferences to provide personalized responses"
        )
        config["tools"] = [tool]

        # Create context manager matching README examples
        cm = ContextManager()
        # Set context values (README examples)
        cm.set_context("user_id", "user123")  # README example
        cm.set_context("preferences", {"theme": "dark"})  # README example

        print("\n🎯 Testing context-aware execution...")
        result = agent_executor(
            user_input="Show me my preferences",
            config=config,
            context_manager=cm
        )

        print(f"Tool selected: {result['selected_tool']}")
        print(f"Parameters: {result['tool_parameters']}")
        print(f"Response: {result['final_response']}")

        # Verify basic response structure
        assert "final_response" in result
        assert len(result["final_response"]) > 0

        # Add small delay to respect API rate limits
        time.sleep(1)


class TestREADMEExamples:
    """Test examples directly from README to ensure documentation accuracy."""

    def test_readme_basic_usage_example(self):
        """Test the exact usage example from README."""
        # README example function
        def my_calculator(params):
            expression = params.get("expression", "0")
            return f"Result: {eval(expression)}"

        # README example config structure
        config = {
            "llm": {
                "model": "gpt-4o-mini",  # User chooses model
                "temperature": 0.7       # User sets temperature
            },
            "system_prompt": "You are my personal AI assistant.",
            "context_limit": 5,
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": False
            },
            # All prompt templates must be provided (see example_config.json)
            "no_tools_prompt": "Analyze this request: {user_input}...",
            "tool_selection_prompt": "Available tools: {tools_text}...",
            "tool_description_template": "• {name}: {description}",
            "tools": []
        }

        # Create tool using create_tool_from_function (as shown in README)
        calculator_tool = create_tool_from_function(
            my_calculator,
            name="calculator",
            description="Performs calculations",
            schema={
                "type": "object",
                "properties": {
                    "expression": {"type": "string"}
                },
                "required": ["expression"]
            }
        )
        config["tools"] = [calculator_tool]

        print("\n📖 Testing README basic usage example...")
        # Execute with user's configuration (README example)
        result = agent_executor("Calculate 15 * 8", config=config)

        print(f"Response: {result['final_response']}")
        print(f"Tool used: {result['selected_tool']}")

        # Verify response structure
        assert "final_response" in result
        assert "selected_tool" in result
        assert len(result["final_response"]) > 0

        time.sleep(1)

    def test_readme_context_example(self):
        """Test the context management example from README."""
        # Example with runtime context (from README)
        from ai_utility_orchestrator import ContextManager

        # Create context manager for session state (README example)
        cm = ContextManager()
        cm.set_context("user_preferences", {"theme": "dark", "language": "en"})

        # Tool that can access runtime context via params["context_manager"] (README)
        def context_aware_tool(params):
            context_manager = params.get("context_manager")
            if context_manager:
                prefs = context_manager.get_context("user_preferences", {})
                return f"Your preferences: theme={prefs.get('theme')}, language={prefs.get('language')}"
            return "No context available"

        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
            "system_prompt": "You are my personal AI assistant.",
            "context_limit": 5,
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "no_tools_prompt": "Analyze this request: {user_input}...",
            "tool_selection_prompt": "Available tools: {tools_text}...",
            "tool_description_template": "• {name}: {description}",
            "tools": [create_tool_from_function(context_aware_tool, name="context_tool", description="Shows user preferences")]
        }

        print("\n🎯 Testing README context example...")
        result = agent_executor(
            "Show my preferences",
            config=config,
            context_manager=cm
        )

        print(f"Context result: {result['final_response']}")

        # Verify response
        assert "final_response" in result
        assert len(result["final_response"]) > 0

        time.sleep(1)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
